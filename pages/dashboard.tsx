import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser, IPartnerConfig } from 'src/types';
import MobileContainer from '@/components/lib/MobileContainer';
import { DASHBOARD_TABS, GLOBALS, QueryParams } from 'src/constants';
import Image from 'next/image';
import classNames from 'classnames';
import { useInfiniteQuery, useQuery } from 'react-query';
import { getAllFormLeads, getV2Campaigns } from 'src/actions/dashboard';
import { getCommonHeaders } from 'src/actions';
import FetchError from 'src/actions/FetchError';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import CampaignsList from '@/components/dashboard/campaigns/CampaignsList';
import LeadsList from '@/components/dashboard/leads/LeadsList';
import Link from 'next/link';
import {
  logApiErrorAndShowToastMessage,
  getLocalStorage,
  setLocalStorage,
} from 'src/utils';
import { IMetaLead } from 'src/types/leads';
import UserProfileImage from '@/components/lib/UserProfileImage';
import { ILeadsCrmDetails } from 'src/types/leads_crm';
import EditLeadsCrmDetailsBs from '@/components/campaign_details/bottom_sheets/EditLeadsCrmDetailsBs';
import { getUserProfile } from 'src/actions/profile';
import Head from 'next/head';
import { ICampaign } from 'src/types/campaigns';
import ChooseCampaignTypeBs from '@/components/dashboard/ChooseCampaignTypeBs';
import { ILeadFilters } from '@/components/dashboard/leads/FilterButtons';

interface IDashboardProps {
  user?: IGroweasyUser;
  partnerConfig?: IPartnerConfig;
}

const SELECTED_TAB_ID_LS_KEY = 'dashboard_selected_tab_id';

const Dashboard = (props: IDashboardProps) => {
  const { user, partnerConfig } = props;

  const [selectedTabId, setSelectedTabId] = useState(DASHBOARD_TABS[0].id);

  // Load saved tab selection from localStorage
  useEffect(() => {
    const savedTabId = getLocalStorage(SELECTED_TAB_ID_LS_KEY);
    if (savedTabId && DASHBOARD_TABS.find((tab) => tab.id === savedTabId)) {
      setSelectedTabId(savedTabId);
    }
  }, []);

  // for pagination in all-leads
  const [startAfterForAllLeads, setStartAfterForAllLeads] = useState<
    string | null
  >(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [allLeads, setAllLeads] = useState<
    Array<IMetaLead & { crm_details?: ILeadsCrmDetails }>
  >([]);
  const [noMoreFormLeadsAvailable, setNoMoreFormLeadsAvailable] =
    useState(false);
  /*const [noMoreCtwaLeadsAvailable, setNoMoreCtwaLeadsAvailable] =
    useState(false);*/
  const [selectedLeadsCrmDetails, setSelectedLeadsCrmDetails] =
    useState<Partial<ILeadsCrmDetails> | null>(null);

  const [campaignTypeSelectionBsVisible, setCampaignTypeSelectionBsVisible] =
    useState(false);
  const [leadFilters, setLeadFilters] = useState<ILeadFilters>({});

  const router = useRouter();

  const campaignsResponse = useInfiniteQuery(
    'getV2Campaigns',
    (params) => {
      return getV2Campaigns({
        headers: getCommonHeaders(user),
        queryParams: {
          ...(router.query as Record<string, string>),
          [QueryParams.LAST_CURSOR_ID]: (params.pageParam as string) ?? '',
          [QueryParams.LIMIT]: `20`,
        },
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      getNextPageParam: (lastPage) => {
        return lastPage.data?.last_cursor_id || undefined;
      },
      onError: (error: FetchError | Error) => {
        logApiErrorAndShowToastMessage(error, 'dashboard.getV2Campaigns');
      },
    },
  );

  // Convert filters to query params
  const getFilterQueryParams = () => {
    const params: Record<string, string> = {};

    if (leadFilters.dateRange) {
      const predefinedRanges = [
        'today',
        'yesterday',
        'last_7_days',
        'last_30_days',
      ];
      const isCustomDateRange = !predefinedRanges.includes(
        leadFilters.dateRange.start,
      );

      if (isCustomDateRange) {
        // Custom date range - dates are in YYYY-MM-DD format
        const startDate = new Date(leadFilters.dateRange.start + 'T00:00:00');
        const endDate = new Date(leadFilters.dateRange.end + 'T23:59:59');
        params.created_time_from = Math.floor(
          startDate.getTime() / 1000,
        ).toString();
        params.created_time_to = Math.floor(
          endDate.getTime() / 1000,
        ).toString();
      } else {
        // Predefined date range
        const today = new Date();
        let startDate = new Date();
        let endDate = new Date();

        switch (leadFilters.dateRange.start) {
          case 'today':
            startDate = new Date(
              today.getFullYear(),
              today.getMonth(),
              today.getDate(),
            );
            endDate = new Date(
              today.getFullYear(),
              today.getMonth(),
              today.getDate(),
              23,
              59,
              59,
            );
            break;
          case 'yesterday':
            startDate = new Date(
              today.getFullYear(),
              today.getMonth(),
              today.getDate() - 1,
            );
            endDate = new Date(
              today.getFullYear(),
              today.getMonth(),
              today.getDate() - 1,
              23,
              59,
              59,
            );
            break;
          case 'last_7_days':
            startDate = new Date(today.getTime() - 6 * 24 * 60 * 60 * 1000);
            startDate.setHours(0, 0, 0, 0);
            endDate = new Date();
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'last_30_days':
            startDate = new Date(today.getTime() - 29 * 24 * 60 * 60 * 1000);
            startDate.setHours(0, 0, 0, 0);
            endDate = new Date();
            endDate.setHours(23, 59, 59, 999);
            break;
        }

        params.created_time_from = Math.floor(
          startDate.getTime() / 1000,
        ).toString();
        params.created_time_to = Math.floor(
          endDate.getTime() / 1000,
        ).toString();
      }
    }

    if (leadFilters.campaign) {
      params.campaign_ids = leadFilters.campaign;
    }

    if (leadFilters.quality) {
      params.lead_category = leadFilters.quality;
    }

    return params;
  };

  const allFormLeadsResponse = useQuery(
    ['getAllFormLeads', startAfterForAllLeads, JSON.stringify(leadFilters)],
    () => {
      const queryParams: Record<string, string> = {
        ...router.query,
        ...getFilterQueryParams(),
      } as Record<string, string>;

      if (isLoadingMore && startAfterForAllLeads) {
        queryParams[QueryParams.START_AFTER] = startAfterForAllLeads;
      }

      return getAllFormLeads({
        headers: getCommonHeaders(user),
        queryParams,
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      onError: (error: FetchError | Error) => {
        logApiErrorAndShowToastMessage(error, 'dashboard.getAllFormLeads');
      },
      onSuccess: (response) => {
        const leads = response.data;
        if (leads?.length) {
          setAllLeads((existingAllLeads) => {
            if (isLoadingMore) {
              return [...existingAllLeads, ...leads].sort(
                (a, b) =>
                  (b.created_time as number) - (a.created_time as number),
              );
            } else {
              return leads.sort(
                (a, b) =>
                  (b.created_time as number) - (a.created_time as number),
              );
            }
          });
        } else {
          if (!isLoadingMore) {
            setAllLeads([]);
          } else {
            // If no more leads during pagination, mark as no more available
            setNoMoreFormLeadsAvailable(true);
          }
        }

        // Reset isLoadingMore flag after processing
        if (isLoadingMore) {
          setIsLoadingMore(false);
        }
      },
    },
  );

  /*const allCtwaLeadsResponse = useQuery(
    ['getAllCtwaLeads', startAfterForAllLeads],
    () => {
      return getAllCtwaLeads({
        headers: getCommonHeaders(user),
        queryParams: {
          ...router.query,
          [QueryParams.START_AFTER]: startAfterForAllLeads,
        } as Record<string, string>,
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      onError: (error: FetchError | Error) => {
        logApiErrorAndShowToastMessage(error, 'dashboard.getAllCtwaLeads');
      },
      onSuccess: (response) => {
        const leads = response.data;
        if (leads?.length) {
          setAllLeads((existingAllLeads) => {
            return [...existingAllLeads, ...leads].sort(
              (a, b) => (b.created_time as number) - (a.created_time as number),
            );
          });
        } else {
          setNoMoreCtwaLeadsAvailable(true);
        }
      },
    },
  );*/

  useQuery(
    'getUserProfile',
    () => {
      return getUserProfile({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'dashboard.getUserProfile');
      },
      onSuccess: (response) => {
        if (response?.data) {
          GLOBALS.userProfile = response.data;
        }
        // new users when dropped from /edit-profile or when press back in flow (signup -> edit-profile -> back)
        if (!response?.data?.mobile_dial_code) {
          void router.push('/edit-profile?source=login');
        }
      },
    },
  );

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  const renderTab = (tabId: string) => {
    const tab = DASHBOARD_TABS.find((item) => item.id === tabId);
    if (!tab) {
      return null;
    }
    const selected = tabId === selectedTabId;
    return (
      <div
        className="cursor-pointer flex-1"
        onClick={() => {
          setSelectedTabId(tabId);
          setLocalStorage(SELECTED_TAB_ID_LS_KEY, tabId);
        }}
      >
        <div
          className={classNames('h-1', {
            'bg-primary w-full': selected,
          })}
        />
        <div className="py-4 flex flex-col items-center">
          <div>
            <Image
              src={selected ? tab.selectedIconUrl : tab.iconUrl}
              width={tab.iconWidth}
              height={tab.iconHeight}
              alt=""
            />
          </div>
          <p
            className={classNames('mt-2 text-xs', {
              'text-primary font-semibold': selected,
              'text-gray-dark': !selected,
            })}
          >
            {tab.label}
          </p>
        </div>
      </div>
    );
  };

  const onCreateCampaignClick = () => {
    setCampaignTypeSelectionBsVisible(true);
  };

  const handleLeadFiltersChange = (filters: ILeadFilters) => {
    setLeadFilters(filters);
    // Reset leads and pagination when filters change
    setAllLeads([]);
    setNoMoreFormLeadsAvailable(false);
    setIsLoadingMore(false);
    setStartAfterForAllLeads(null);
  };

  // details will have campaign_id from child
  const onEditLeadsCrmClick = (details: Partial<ILeadsCrmDetails>) => {
    details.uid = user?.uid;
    setSelectedLeadsCrmDetails(details);
  };

  const onLeadsCrmDetailsUpdate = (
    updatedLeadsCrmDetails: ILeadsCrmDetails,
  ) => {
    // update crm_details in leads
    const allLeadsCopy = [...allLeads];
    for (let i = 0; i < allLeadsCopy.length; i++) {
      if (allLeadsCopy[i].id === updatedLeadsCrmDetails.leadgen_id) {
        allLeadsCopy[i].crm_details = updatedLeadsCrmDetails;
        break;
      }
    }
    setAllLeads(allLeadsCopy);
  };

  const onNextLeadsPress = () => {
    const lastLeadInList = allLeads[allLeads.length - 1];
    if (lastLeadInList && !isLoadingMore && !noMoreFormLeadsAvailable) {
      setIsLoadingMore(true);
      setStartAfterForAllLeads(`${lastLeadInList.created_time}`);
      // Trigger refetch after state is updated
      setTimeout(() => {
        void allFormLeadsResponse.refetch();
      }, 0);
    }
  };

  const campaigns: ICampaign[] = [];
  campaignsResponse.data?.pages?.forEach((item) => {
    campaigns.push(...item.data.campaigns);
  });

  const isPageInitialLoading = (() => {
    if (selectedTabId === DASHBOARD_TABS[0].id) {
      return campaignsResponse.isLoading;
    }
    if (selectedTabId === DASHBOARD_TABS[1].id) {
      return allFormLeadsResponse.isLoading;
    }
    return false;
  })();

  return user ? (
    <MobileContainer>
      <Head>
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      <div className="flex flex-col flex-1 h-full w-full pt-4">
        <div className="flex items-center px-5">
          <Link
            className="text-black text-xl font-semibold tracking-tight"
            href="/"
          >
            {partnerConfig?.name ?? 'GrowEasy'}
          </Link>
          <div className="flex-1" />
          <div className="p-3">
            <Link href="/profile">
              <UserProfileImage user={user} width={24} height={24} />
            </Link>
          </div>
        </div>
        <div className="flex-1 overflow-y-scroll flex flex-col no-scrollbar px-5">
          {isPageInitialLoading ? (
            <div className="flex-1 flex items-center justify-center">
              <SpinnerLoader size={40} />
            </div>
          ) : (
            <>
              {selectedTabId === DASHBOARD_TABS[0].id ? (
                <div className="flex flex-col flex-1 mt-6">
                  <CampaignsList
                    data={campaigns}
                    onCreateCampaignClick={onCreateCampaignClick}
                  />
                  {campaignsResponse.hasNextPage ? (
                    <div className="my-3">
                      {campaignsResponse.isFetching ? (
                        <div className="flex justify-center">
                          <SpinnerLoader borderWidth={2} size={20} />
                        </div>
                      ) : (
                        <p
                          className="text-sm text-center text-hyperlink cursor-pointer"
                          onClick={() => {
                            void campaignsResponse.fetchNextPage();
                          }}
                        >
                          Load More
                        </p>
                      )}
                    </div>
                  ) : null}
                </div>
              ) : null}
              {selectedTabId === DASHBOARD_TABS[1].id ? (
                <div className="flex flex-col flex-1 items-center">
                  <LeadsList
                    data={allLeads}
                    onNextPress={onNextLeadsPress}
                    noMoreLeadsAvailable={
                      noMoreFormLeadsAvailable //&& noMoreCtwaLeadsAvailable
                    }
                    loading={
                      allFormLeadsResponse.isFetching || isLoadingMore
                      // || allCtwaLeadsResponse.isFetching
                    }
                    campaigns={campaigns}
                    onEditLeadsCrmClick={onEditLeadsCrmClick}
                    partnerConfig={partnerConfig}
                    onFiltersChange={handleLeadFiltersChange}
                    activeFilters={leadFilters}
                  />
                </div>
              ) : null}
            </>
          )}
        </div>
        <div className="w-full flex items-center bg-white mt-3">
          {renderTab(DASHBOARD_TABS[0].id)}
          <div
            className="flex-1 flex flex-col items-center cursor-pointer"
            onClick={onCreateCampaignClick}
          >
            <div className="w-12 h-12 rounded-full flex items-center justify-center bg-primary -mt-6">
              <Image
                src="/images/dashboard/add.png"
                width="20"
                height="20"
                alt=""
              />
            </div>
            <p className="text-xs text-gray-dark mt-2">New Campaign</p>
          </div>
          {renderTab(DASHBOARD_TABS[1].id)}
        </div>
        {selectedLeadsCrmDetails ? (
          <EditLeadsCrmDetailsBs
            leadsCrmDetails={selectedLeadsCrmDetails}
            onClose={() => setSelectedLeadsCrmDetails(null)}
            user={user}
            onUpdateDone={onLeadsCrmDetailsUpdate}
          />
        ) : null}
        {campaignTypeSelectionBsVisible ? (
          <ChooseCampaignTypeBs
            onClose={() => setCampaignTypeSelectionBsVisible(false)}
            partnerConfig={partnerConfig}
            user={user}
          />
        ) : null}
      </div>
    </MobileContainer>
  ) : null;
};

export default Dashboard;
