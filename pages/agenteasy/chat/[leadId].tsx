import { useRouter } from 'next/router';
import { useEffect, useRef } from 'react';
import { useQuery } from 'react-query';
import MobileContainer from '@/components/lib/MobileContainer';
import BackIcon from '@/images/common/back-arrow.svg';
import Head from 'next/head';
import { getCommonHeaders } from 'src/actions';
import {
  getAgentEasyChatMessages,
  getAgentEasyConversationDetails,
} from 'src/actions/agenteasy';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import { IGroweasyUser } from 'src/types';
import {
  IAgentEasyMessage,
  AgentEasyMessageRole,
  IAgentEasyTimestamp,
} from 'src/types/agenteasy';
import SpinnerLoader from '@/components/lib/SpinnerLoader';

interface IChatPageProps {
  user: IGroweasyUser;
}

const ChatPage = ({ user }: IChatPageProps) => {
  const router = useRouter();
  const { leadId } = router.query;
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const chatResponse = useQuery(
    ['getAgentEasyChatMessages', leadId],
    () => {
      return getAgentEasyChatMessages({
        headers: getCommonHeaders(user),
        queryParams: {
          chat_doc_id: leadId as string,
        },
      });
    },
    {
      retry: 0,
      enabled: !!leadId && !!user?.authToken,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'AgentEasyChat.getAgentEasyChatMessages',
        );
      },
    },
  );

  const conversationDetailsResponse = useQuery(
    ['getAgentEasyConversationDetails', leadId],
    () => {
      return getAgentEasyConversationDetails({
        headers: getCommonHeaders(user),
        queryParams: {
          chat_doc_id: leadId as string,
        },
      });
    },
    {
      retry: 0,
      enabled: !!leadId && !!user?.authToken,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'AgentEasyChat.getAgentEasyConversationDetails',
        );
      },
    },
  );

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatResponse.data]);

  const formatTime = (timestamp: IAgentEasyTimestamp): string => {
    try {
      const date = timestamp?.toDate
        ? timestamp.toDate()
        : new Date(timestamp._seconds * 1000);
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      });
    } catch (error) {
      return 'Invalid time';
    }
  };

  const formatDate = (timestamp: IAgentEasyTimestamp): string => {
    try {
      const date = timestamp?.toDate
        ? timestamp.toDate()
        : new Date(timestamp._seconds * 1000);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      });
    } catch (error) {
      return 'Invalid date';
    }
  };

  const getLeadCategoryBadgeColor = (category: string): string => {
    switch (category?.toLowerCase()) {
      case 'hot':
        return 'bg-red/15 text-red';
      case 'warm':
        return 'bg-orange-100 text-orange-800';
      case 'cold':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getMessageText = (message: IAgentEasyMessage): string => {
    return message.payload?.text?.body || 'Message content unavailable';
  };

  const messages = chatResponse.data?.data || [];
  const conversationDetails = conversationDetailsResponse.data?.data;

  if (chatResponse.isLoading || conversationDetailsResponse.isLoading) {
    return (
      <MobileContainer>
        <div className="flex items-center justify-center h-full">
          <SpinnerLoader />
        </div>
      </MobileContainer>
    );
  }

  if (chatResponse.isError || conversationDetailsResponse.isError) {
    return (
      <MobileContainer>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <p className="text-gray-dark">
              Failed to load chat. Please try again.
            </p>
          </div>
        </div>
      </MobileContainer>
    );
  }

  return (
    <MobileContainer>
      <Head>
        <title>AI Chat - GrowEasy</title>
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className="flex flex-col h-full bg-off-white">
        {/* Header */}
        <div className="bg-white border-b border-gray-light">
          <div className="px-4 py-3">
            <div className="flex items-center">
              <button
                type="button"
                onClick={() => router.back()}
                className="mr-3 p-1"
              >
                <BackIcon className="w-5 h-5 text-gray-dark" />
              </button>
              <div className="flex-1">
                <h1 className="text-base font-semibold text-gray-dark">
                  {conversationDetails?.campaign_context?.full_name ||
                    'AI Chat'}
                </h1>
                <div className="text-xs text-gray-500">Chat ID: {leadId}</div>
              </div>
            </div>
          </div>

          {/* Lead Details Section */}
          {conversationDetails && (
            <div className="px-4 pb-3 border-t border-gray-100">
              <div className="flex items-center justify-between mt-3">
                <div className="flex items-center space-x-2">
                  <span className="text-xs font-medium text-gray-600">
                    Lead Status:
                  </span>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getLeadCategoryBadgeColor(
                      conversationDetails.campaign_context.classification_data
                        .lead_category,
                    )}`}
                  >
                    {
                      conversationDetails.campaign_context.classification_data
                        .lead_category
                    }
                  </span>
                </div>
                <div className="text-xxs text-gray-500">
                  {conversationDetails.campaign_context.chat_status}
                </div>
              </div>

              {/* Lead Summary */}
              {conversationDetails.campaign_context.classification_data
                .lead_summary && (
                <div className="mt-3 p-3 bg-teal-100/30 border border-primary2/40 rounded-lg">
                  <div className="text-sm font-medium text-primary mb-1">
                    Lead Summary
                  </div>
                  <div className="text-sm text-primary2">
                    {
                      conversationDetails.campaign_context.classification_data
                        .lead_summary
                    }
                  </div>
                </div>
              )}

              {/* Last Activity */}
              <div className="mt-3 flex justify-between items-center text-xxs text-gray-500">
                <div>Created: {formatDate(conversationDetails.created_at)}</div>
                <div>
                  Last Reply:{' '}
                  {formatDate(conversationDetails.user_last_reply_time)}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Chat Messages */}
        <div className="flex-1 overflow-y-auto px-4 py-4 space-y-4">
          {messages.map((msg, index) => (
            <div
              key={index}
              className={`flex ${
                msg.role === AgentEasyMessageRole.USER
                  ? 'justify-end'
                  : 'justify-start'
              }`}
            >
              <div className="max-w-[80%]">
                {msg.role === AgentEasyMessageRole.ASSISTANT && (
                  <div className="text-xs text-gray-500 mb-1">Agenteasy AI</div>
                )}
                <div
                  className={`rounded-lg shadow shadow-gray-400/20 px-3 py-2 ${
                    msg.role === AgentEasyMessageRole.USER
                      ? 'bg-primary text-white'
                      : 'bg-white border border-gray-light'
                  }`}
                >
                  <p
                    className={`text-sm ${
                      msg.role === AgentEasyMessageRole.USER
                        ? 'text-white'
                        : 'text-black'
                    }`}
                  >
                    {getMessageText(msg)}
                  </p>
                </div>
                <div
                  className={`text-[9px] text-gray-500 mt-1 ${
                    msg.role === AgentEasyMessageRole.USER
                      ? 'text-right'
                      : 'text-left'
                  }`}
                >
                  {formatTime(msg.time)}
                </div>
              </div>
            </div>
          ))}

          <div className="text-center text-gray-500">--- The End ---</div>
          <div ref={messagesEndRef} />
        </div>
      </div>
    </MobileContainer>
  );
};

export default ChatPage;
