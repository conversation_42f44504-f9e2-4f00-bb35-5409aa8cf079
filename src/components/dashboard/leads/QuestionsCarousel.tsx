import React, { useState } from 'react';
import { BiCalendar, BiChevronLeft, BiChevronRight } from 'react-icons/bi';
import { useSwipeable } from 'react-swipeable';

interface Question {
  question: string;
  answer?: string;
  date?: string;
}

interface QuestionCarouselProps {
  questions: Question[];
}

const QuestionCarousel: React.FC<QuestionCarouselProps> = ({ questions }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % questions.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev === 0 ? questions.length - 1 : prev - 1));
  };

  const handlers = useSwipeable({
    onSwipedLeft: () => nextSlide(),
    onSwipedRight: () => prevSlide(),
    trackMouse: true,
  });

  if (!questions.length) return null;

  const question = questions[currentIndex];

  return (
    <div className="relative flex flex-col items-center w-full" {...handlers}>
      <div className="w-full md:w-[98%] bg-white rounded-xl border border-gray-200 p-4 shadow-sm transition-all duration-200">
        <p className="text-sm text-gray-600 mb-2">{question.question}</p>
        <p className="text-base font-semibold text-primary2">
          {question.answer || 'No answer provided'}
        </p>

        {question.date && (
          <div className="flex items-center text-xs text-gray-500">
            <BiCalendar size={14} className="mr-1" />
            <span>{question.date}</span>
          </div>
        )}
      </div>

      {questions.length > 1 && (
        <>
          <button
            onClick={prevSlide}
            className="absolute -left-2 top-1/2 -translate-y-1/2 z-10 w-5 h-5 bg-white rounded-full shadow-md border border-gray-200 items-center justify-center hover:bg-gray-50 transition-colors hidden sm:flex"
          >
            <BiChevronLeft size={20} className="text-gray-600" />
          </button>
          <button
            onClick={nextSlide}
            className="absolute -right-2 top-1/2 -translate-y-1/2 z-10 w-5 h-5 bg-white rounded-xl shadow-md border border-gray-200 items-center justify-center hover:bg-gray-50 transition-colors hidden sm:flex"
          >
            <BiChevronRight size={20} className="text-gray-600" />
          </button>
        </>
      )}

      {questions.length > 1 && (
        <div className="flex justify-center mt-2 space-x-2">
          {questions.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-2 h-2 rounded-full transition-all duration-200 ${
                index === currentIndex
                  ? 'bg-primary2'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default QuestionCarousel;
