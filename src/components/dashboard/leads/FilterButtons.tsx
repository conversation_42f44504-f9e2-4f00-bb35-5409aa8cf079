import { useState, useEffect, useCallback } from 'react';
import CrossIcon from '@/images/common/cross.svg';
import { ILeadsStatus } from 'src/types/leads_crm';
import { ICampaign, GROWEASY_CAMPAIGN_TYPE } from 'src/types/campaigns';
import BottomSheet from '@/components/lib/BottomSheet';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { FaCalendarAlt } from 'react-icons/fa';
import { BiChevronRight } from 'react-icons/bi';

// Props are updated to accept activeFilters from the parent
export interface IFilterButtonsProps {
  campaigns: ICampaign[];
  onFiltersChange: (filters: ILeadFilters) => void;
  activeFilters: ILeadFilters;
}

export interface ILeadFilters {
  status?: ILeadsStatus;
  dateRange?: {
    start: string;
    end: string;
  };
  quality?: 'HOT' | 'COLD' | 'WARM';
  campaign?: string;
}

type FilterTab = 'status' | 'date' | 'quality' | 'campaign';

interface IFilterChip {
  key: string;
  label: string;
  onRemove: () => void;
}

const FilterButtons = (props: IFilterButtonsProps) => {
  const { campaigns, onFiltersChange, activeFilters } = props;
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<FilterTab>('date');
  const [tempFilters, setTempFilters] = useState<ILeadFilters>({});
  const [customDateRange, setCustomDateRange] = useState<{
    start: Date | null;
    end: Date | null;
  }>({ start: null, end: null });
  const [selectedDateOption, setSelectedDateOption] = useState<string>('');

  const dateOptions = [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'last_7_days', label: 'Last 7 days' },
    { value: 'last_30_days', label: 'Last 30 days' },
    { value: 'custom', label: 'Custom range' },
  ];

  const qualityOptions = [
    { value: 'HOT', label: 'Hot' },
    { value: 'COLD', label: 'Cold' },
    { value: 'WARM', label: 'Warm' },
  ];

  const openDrawer = (defaultTab?: FilterTab) => {
    setTempFilters({ ...activeFilters });

    if (activeFilters.dateRange) {
      const existingOption = dateOptions.find(
        (opt) => opt.value === activeFilters.dateRange?.start,
      );
      if (existingOption) {
        setSelectedDateOption(existingOption.value);
      } else {
        setSelectedDateOption('custom');
        if (activeFilters.dateRange.start && activeFilters.dateRange.end) {
          setCustomDateRange({
            start: new Date(activeFilters.dateRange.start),
            end: new Date(activeFilters.dateRange.end),
          });
        }
      }
    } else if (defaultTab === 'date') {
      setSelectedDateOption('today');
      setTempFilters({
        ...activeFilters,
        dateRange: { start: 'today', end: 'today' },
      });
    }
    if (defaultTab) {
      setActiveTab(defaultTab);
    }

    setIsDrawerOpen(true);
  };

  const closeDrawer = () => {
    setIsDrawerOpen(false);
    setActiveTab('status');
    setSelectedDateOption('');
    setCustomDateRange({ start: null, end: null });
  };

  const applyFilters = () => {
    onFiltersChange(tempFilters);
    closeDrawer();
  };

  const handleTempFilterSelect = (filterType: string, value: string) => {
    const newFilters = { ...tempFilters };

    if (filterType === 'campaign') {
      // This makes campaign a toggleable checkbox
      if (newFilters.campaign === value) {
        delete newFilters.campaign;
      } else {
        newFilters.campaign = value;
      }
    } else if (filterType === 'quality') {
      // This makes quality a toggleable checkbox
      if (newFilters.quality === value) {
        delete newFilters.quality;
      } else {
        newFilters.quality = value as 'HOT' | 'COLD' | 'WARM';
      }
    } else if (filterType === 'date') {
      if (value === 'custom') {
        setSelectedDateOption('custom');
        if (newFilters.dateRange) {
          delete newFilters.dateRange;
        }
      } else {
        setSelectedDateOption(value);
        newFilters.dateRange = { start: value, end: value };
        setCustomDateRange({ start: null, end: null });
      }
    }

    setTempFilters(newFilters);
  };

  const handleCustomDateChange = useCallback(() => {
    if (customDateRange.start && customDateRange.end) {
      const newFilters = { ...tempFilters };
      newFilters.dateRange = {
        start: customDateRange.start.toLocaleDateString('en-CA'), // YYYY-MM-DD format
        end: customDateRange.end.toLocaleDateString('en-CA'), // YYYY-MM-DD format
      };
      setTempFilters(newFilters);
    }
  }, [customDateRange.start, customDateRange.end, tempFilters]);

  useEffect(() => {
    if (
      selectedDateOption === 'custom' &&
      customDateRange.start &&
      customDateRange.end
    ) {
      handleCustomDateChange();
    }
  }, [
    customDateRange.start,
    customDateRange.end,
    selectedDateOption,
    handleCustomDateChange,
  ]);

  const removeFilter = (filterType: string) => {
    const newFilters = { ...activeFilters };

    if (filterType === 'campaign') {
      delete newFilters.campaign;
    } else if (filterType === 'quality') {
      delete newFilters.quality;
    } else if (filterType === 'date') {
      delete newFilters.dateRange;
      setSelectedDateOption('');
      setCustomDateRange({ start: null, end: null });
    }

    onFiltersChange(newFilters);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'date':
        return (
          <div className="space-y-4">
            {dateOptions.map((option) => (
              <label
                key={option.value}
                className="flex items-center space-x-3 cursor-pointer"
              >
                <input
                  type="radio"
                  name="dateOption"
                  checked={selectedDateOption === option.value}
                  onChange={() => handleTempFilterSelect('date', option.value)}
                  className="w-4 h-4 text-primary2 border-gray-300 focus:ring-primary2"
                />
                <span className="text-sm">{option.label}</span>
              </label>
            ))}

            {selectedDateOption === 'custom' && (
              <div className="mt-4 space-y-3 pl-7">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Start Date
                  </label>
                  <DatePicker
                    selected={customDateRange.start}
                    onChange={(date: Date | null) => {
                      setCustomDateRange((prev) => ({ ...prev, start: date }));
                    }}
                    selectsStart
                    startDate={customDateRange.start}
                    endDate={customDateRange.end}
                    placeholderText="Select start date"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary2"
                    showIcon
                    icon={<FaCalendarAlt />}
                    calendarIconClassName="!w-3 !h-3"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    End Date
                  </label>
                  <DatePicker
                    selected={customDateRange.end}
                    onChange={(date: Date | null) => {
                      setCustomDateRange((prev) => ({ ...prev, end: date }));
                    }}
                    selectsEnd
                    startDate={customDateRange.start}
                    endDate={customDateRange.end}
                    minDate={customDateRange.start}
                    placeholderText="Select end date"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary2"
                    showIcon
                    icon={<FaCalendarAlt />}
                    calendarIconClassName="!w-3 !h-3"
                  />
                </div>
              </div>
            )}
          </div>
        );

      case 'quality':
        return (
          <div className="space-y-3">
            {qualityOptions.map((option) => (
              <label
                key={option.value}
                className="flex items-center space-x-3 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={tempFilters.quality === option.value}
                  onChange={() =>
                    handleTempFilterSelect('quality', option.value)
                  }
                  className="w-4 h-4 text-primary2 border-gray-300 rounded focus:ring-primary2"
                />
                <span className="text-sm">{option.label}</span>
              </label>
            ))}
          </div>
        );

      case 'campaign':
        return (
          <div className="space-y-3">
            {campaignOptions.map((option) => (
              <label
                key={option.value}
                className="flex items-center space-x-3 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={tempFilters.campaign === option.value}
                  onChange={() =>
                    handleTempFilterSelect('campaign', option.value)
                  }
                  className="w-4 h-4 text-primary2 border-gray-300 rounded focus:ring-primary2"
                />
                <span className="text-sm">{option.label}</span>
              </label>
            ))}
          </div>
        );

      default:
        return null;
    }
  };

  // This now uses the activeFilters from props
  const getActiveFilterChips = (): IFilterChip[] => {
    const chips: IFilterChip[] = [];

    if (activeFilters.dateRange) {
      const preDefinedOption = dateOptions.find(
        (opt) => opt.value === activeFilters.dateRange?.start,
      );

      let displayLabel = `Date: ${preDefinedOption?.label || 'Custom'}`;

      if (
        !preDefinedOption &&
        activeFilters.dateRange.start &&
        activeFilters.dateRange.end
      ) {
        try {
          const startDate = new Date(activeFilters.dateRange.start);
          const endDate = new Date(activeFilters.dateRange.end);

          if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
            const startFormatted = startDate.toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              year: 'numeric',
            });
            const endFormatted = endDate.toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              year: 'numeric',
            });
            displayLabel = `Date: ${startFormatted} - ${endFormatted}`;
          }
        } catch (error) {
          console.error('Error formatting custom date range:', error);
          displayLabel = 'Date: Custom Range';
        }
      }

      chips.push({
        key: 'date',
        label: displayLabel,
        onRemove: () => removeFilter('date'),
      });
    }

    if (activeFilters.quality) {
      const qualityLabel = qualityOptions.find(
        (q) => q.value === activeFilters.quality,
      )?.label;
      chips.push({
        key: 'quality',
        label: `Quality: ${qualityLabel}`,
        onRemove: () => removeFilter('quality'),
      });
    }

    if (activeFilters.campaign) {
      const campaignLabel =
        campaigns.find((c) => c.id === activeFilters.campaign)?.friendly_name ||
        'Unknown';
      chips.push({
        key: 'campaign',
        label: `Campaign: ${campaignLabel}`,
        onRemove: () => removeFilter('campaign'),
      });
    }

    return chips;
  };

  const campaignOptions = campaigns
    .filter((campaign) => campaign.type === GROWEASY_CAMPAIGN_TYPE.LEAD_FORM)
    .map((campaign) => ({
      value: campaign.id,
      label: campaign.friendly_name || campaign.name || 'Unnamed Campaign',
    }));

  const tabs = [
    { id: 'date' as FilterTab, label: 'Date' },
    { id: 'quality' as FilterTab, label: 'Quality' },
    { id: 'campaign' as FilterTab, label: 'Campaign' },
  ];

  const activeFilterChips = getActiveFilterChips();

  return (
    <div className="mb-4 sticky top-0 bg-off-white pb-1">
      <div className="pb-2">
        <div className="flex gap-2 overflow-x-auto py-2 no-scrollbar">
          <button
            type="button"
            className="flex items-center px-3 sm:px-4 py-2 border border-gray-300 rounded-lg text-sm bg-white shadow"
            onClick={() => openDrawer('date')}
          >
            <span>Date</span>
            <BiChevronRight className="ml-2 w-3 h-3 text-gray-500" />
          </button>
          <button
            type="button"
            className="flex items-center px-3 sm:px-4 py-2 border border-gray-300 rounded-lg text-sm bg-white shadow"
            onClick={() => openDrawer('quality')}
          >
            <span>Quality</span>
            <BiChevronRight className="ml-2 w-3 h-3 text-gray-500" />
          </button>
          <button
            type="button"
            className="flex items-center px-3 sm:px-4 py-2 border border-gray-300 rounded-lg text-sm bg-white shadow"
            onClick={() => openDrawer('campaign')}
          >
            <span>Campaign</span>
            <BiChevronRight className="ml-2 w-3 h-3 text-gray-500" />
          </button>
        </div>
      </div>

      {/* Active Filter Chips */}
      {activeFilterChips.length > 0 && (
        <div className="flex gap-2 flex-wrap sticky top-20">
          {activeFilterChips.map((chip) => (
            <div
              key={chip.key}
              className="flex items-center px-2 py-1.5 sm:px-4 bg-[#D5E5E3] border-primary2/40 border rounded-full text-xs backdrop-blur"
            >
              <span>{chip.label}</span>
              <button
                type="button"
                className="ml-3 text-gray-500 hover:text-gray-700"
                onClick={chip.onRemove}
              >
                <CrossIcon className="size-2" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Bottom Drawer */}
      {isDrawerOpen && (
        <BottomSheet
          onClose={closeDrawer}
          className="h-4/5"
          contentClassName="h-full"
        >
          <div className="flex flex-col h-full flex-1">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-gray-900">Filter</h2>
              <button
                type="button"
                onClick={closeDrawer}
                className="text-gray-500 hover:text-gray-700"
              >
                <CrossIcon className="w-5 h-5" />
              </button>
            </div>
            <div className="flex border-b border-gray-200 mb-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === tab.id
                      ? 'border-primary2 text-primary2'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
            <div className="flex-1 overflow-y-auto no-scrollbar pb-16">
              {renderTabContent()}
            </div>
            <div className="sticky z-20 bottom-20 bg-white py-4 border-t border-gray-200 mt-auto">
              <button
                type="button"
                onClick={applyFilters}
                className="w-full bg-primary2 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary transition-colors"
              >
                Apply
              </button>
            </div>
          </div>
        </BottomSheet>
      )}
    </div>
  );
};

export default FilterButtons;
